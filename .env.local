APP_ENV=development
APP_NAME=LumiAPI
APP_KEY=base64:1cDTffCEZpoPEt283WQZo42JRRbkCsNUgf5s9+/QbqI=
APP_DEBUG=true
APP_TIMEZONE='America/Sao_Paulo'
APP_URL=http://local.api.lumi
FRONTEND_URL=http://local.api.lumi
LUMIVISION_URL=http://local.lumivision

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lumi
DB_USERNAME=lumi_app
DB_PASSWORD=lumi_app123

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# MAIL_MAILER=log
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=sua-senha-de-app
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="Lumi Ortho System"

MAIL_MAILER=smtp
MAIL_HOST=smtp.lumivision.app
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=myC9=Mn7@E=a9
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Vision"
MAIL_VERIFY_PEER=false

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

AUTH_GUARD="api"

JWT_SECRET=1CC2K9fc7qCOOehSWLHvAd3t0QboAuSb43HhMlWztgSCVcp1blMTlRl7ctveUPzx