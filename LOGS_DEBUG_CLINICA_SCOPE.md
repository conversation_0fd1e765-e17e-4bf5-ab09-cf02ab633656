# 🔍 Debug Logs - ClinicaScope

Este documento explica como visualizar e interpretar os logs de debug adicionados para entender o comportamento do `ClinicaScope`.

## 📋 O que foi adicionado

### 1. Logs no ClinicaScope (`app/Scopes/ClinicaScope.php`)

O scope agora registra informações detalhadas toda vez que é aplicado:

- **🔍 ClinicaScope aplicado**: Indica que o scope foi chamado
  - `model`: Classe do modelo
  - `table`: Nome da tabela
  - `auth_check`: Se há usuário autenticado

- **👤 Usuário autenticado**: Informações do usuário
  - `user_id`: ID do usuário
  - `clinica_id`: ID da clínica do usuário
  - `clinica_slug`: Slug da clínica
  - `system_admin`: Se é admin do sistema
  - `model`: Modelo sendo consultado

- **✅ Filtro aplicado**: Quando o filtro é aplicado (usuário não-admin)
  - `model`: Classe do modelo
  - `table`: Nome da tabela
  - `clinica_id`: ID da clínica filtrada
  - `condition`: Condição SQL aplicada

- **⚠️ Filtro NÃO aplicado**: Quando o filtro não é aplicado (admin)
  - `model`: Classe do modelo
  - `user_id`: ID do usuário admin
  - `has_clinica`: Se o usuário tem clínica associada
  - `is_admin`: Confirmação de que é admin

- **❌ Usuário não autenticado**: Quando não há autenticação
  - `model`: Classe do modelo

### 2. Logs nos Controllers

#### PacientesController
- **👥 Pacientes::index()**: Quando a listagem é chamada
- **📊 Pacientes encontrados**: Resultado da consulta
  - `count`: Quantidade de pacientes
  - `clinicas`: IDs únicos das clínicas dos pacientes

#### Dentistas Controller
- **📋 Dentistas::index()**: Quando a listagem é chamada
- **📊 Dentistas encontrados**: Resultado da consulta
  - `count`: Quantidade de dentistas
  - `clinicas`: IDs únicos das clínicas dos dentistas
- **🔎 Dentistas::show()**: Quando busca um dentista específico
  - `id_matricula`: ID da matrícula buscada
- **✅ Dentista encontrado**: Resultado da busca
  - `id`: ID do dentista
  - `id_matricula`: ID da matrícula
  - `clinica_id`: ID da clínica
  - `nome`: Nome do dentista

#### FinanceiroReceberController
- **💰 FinanceiroReceber::index()**: Quando a listagem é chamada
  - `filtros`: Filtros aplicados na requisição
- **📊 Faturas encontradas**: Resultado da consulta
  - `total`: Total de registros (paginação)
  - `count`: Quantidade na página atual
  - `clinicas`: IDs únicos das clínicas das faturas
- **🔎 FinanceiroReceber::show()**: Quando busca uma fatura específica
  - `id`: ID da fatura buscada
- **✅ Fatura encontrada**: Resultado da busca
  - `id`: ID da fatura
  - `clinica_id`: ID da clínica
  - `paciente_id`: ID do paciente
  - `valor_final`: Valor da fatura

## 📖 Como visualizar os logs

### Opção 1: Arquivo de log do Laravel

```bash
# No Windows (Git Bash ou PowerShell)
tail -f storage/logs/laravel.log

# Ou visualizar as últimas 100 linhas
tail -n 100 storage/logs/laravel.log

# Filtrar apenas logs do ClinicaScope
grep "ClinicaScope" storage/logs/laravel.log

# Filtrar logs de um controller específico
grep "Dentistas::" storage/logs/laravel.log
```

### Opção 2: Laravel Telescope (se instalado)

Se você tiver o Laravel Telescope instalado:
1. Acesse: `http://seu-dominio/telescope`
2. Vá para a aba "Logs"
3. Filtre por nível "info"

### Opção 3: Visualizar em tempo real

```bash
# Limpar o log atual
echo "" > storage/logs/laravel.log

# Iniciar visualização em tempo real
tail -f storage/logs/laravel.log
```

## 🧪 Como testar

### 1. Teste com usuário não-admin

```bash
# Faça login com um usuário normal
# Depois faça uma requisição para listar dentistas
curl -X GET http://localhost:8000/api/dentistas \
  -H "Authorization: Bearer SEU_TOKEN"
```

**Logs esperados:**
```
🔍 ClinicaScope aplicado - model: App\Models\Dentista
👤 Usuário autenticado - clinica_id: 1, system_admin: false
✅ Filtro aplicado - clinica_id: 1, condition: dentistas.clinica_id = 1
📋 Dentistas::index() chamado
📊 Dentistas encontrados - count: 5, clinicas: [1]
```

### 2. Teste com admin

```bash
# Faça login com um admin
# Depois faça uma requisição para listar dentistas
curl -X GET http://localhost:8000/api/dentistas \
  -H "Authorization: Bearer SEU_TOKEN_ADMIN"
```

**Logs esperados:**
```
🔍 ClinicaScope aplicado - model: App\Models\Dentista
👤 Usuário autenticado - clinica_id: 1, system_admin: true
⚠️ Filtro NÃO aplicado (admin) - user_id: 1
📋 Dentistas::index() chamado
📊 Dentistas encontrados - count: 15, clinicas: [1, 2, 3, 4]
```

### 3. Teste busca específica

```bash
# Buscar um dentista específico
curl -X GET http://localhost:8000/api/dentistas/1 \
  -H "Authorization: Bearer SEU_TOKEN"
```

**Logs esperados:**
```
🔍 ClinicaScope aplicado - model: App\Models\Dentista
👤 Usuário autenticado - clinica_id: 1, system_admin: false
✅ Filtro aplicado - clinica_id: 1
🔎 Dentistas::show() chamado - id_matricula: 1
✅ Dentista encontrado - id: 5, clinica_id: 1, nome: Dr. João
```

## 🔧 Interpretando os resultados

### ✅ Comportamento correto (usuário não-admin)

- O scope deve ser aplicado
- O filtro deve ser aplicado com a `clinica_id` do usuário
- Os resultados devem conter apenas registros da clínica do usuário
- O array `clinicas` deve conter apenas um ID (da clínica do usuário)

### ✅ Comportamento correto (admin)

- O scope deve ser aplicado
- O filtro NÃO deve ser aplicado
- Os resultados podem conter registros de múltiplas clínicas
- O array `clinicas` pode conter múltiplos IDs

### ❌ Problemas possíveis

1. **Filtro não aplicado para usuário não-admin**
   - Verifique se `system_admin` está `false`
   - Verifique se `clinica_id` está presente no payload do usuário

2. **Filtro aplicado para admin**
   - Verifique se `system_admin` está `true`
   - Pode ser necessário usar `withoutGlobalScope()` em casos específicos

3. **Usuário não autenticado**
   - Verifique se o token JWT está válido
   - Verifique se o middleware de autenticação está aplicado

## 🧹 Removendo os logs

Quando terminar o debug, você pode remover os logs:

1. **No ClinicaScope**: Remova todas as chamadas `Log::info()` e `Log::warning()`
2. **Nos Controllers**: Remova todas as chamadas `\Log::info()`

Ou mantenha apenas os logs mais importantes para monitoramento em produção.

## 📝 Notas importantes

- Os logs são gravados em `storage/logs/laravel.log`
- Em produção, considere usar um nível de log diferente (como `debug`) para não poluir os logs
- Os emojis ajudam a identificar rapidamente o tipo de log
- Todos os logs incluem contexto relevante para facilitar o debug

## 🎯 Próximos passos

1. Execute as requisições de teste
2. Analise os logs gerados
3. Verifique se o comportamento está correto
4. Ajuste o código se necessário
5. Remova ou ajuste os logs conforme necessário

