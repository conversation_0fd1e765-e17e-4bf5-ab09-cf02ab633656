<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FinanceiroPagar;
use Illuminate\Support\Facades\Validator;

class FinanceiroPagarController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = FinanceiroPagar::with(['clinica']);

        // Filtros
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('descricao')) {
            $query->where('descricao', 'LIKE', '%' . $request->descricao . '%');
        }

        if ($request->has('data_inicio') && $request->has('data_fim')) {
            $query->whereBetween('data_vencimento', [
                $request->data_inicio,
                $request->data_fim
            ]);
        }

        if ($request->has('vencidas') && $request->vencidas) {
            $query->where('data_vencimento', '<', now()->toDateString())
                  ->where('status', '!=', 'pago');
        }

        $contas = $query->orderBy('data_vencimento', 'desc')
                       ->orderBy('created_at', 'desc')
                       ->paginate(15);

        return response()->json($contas);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();

        $validator = Validator::make($request->all(), [
            'descricao' => 'required|string|max:255',
            'valor_nominal' => 'required|numeric|min:0.01',
            'data_vencimento' => 'required|date',
            'fornecedor_id' => 'nullable|integer',
            'paciente_id' => 'nullable|integer',
            'referencia' => 'nullable|string|max:255',
            'notas' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Dados inválidos',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $dados = $request->all();
            $dados['clinica_id'] = $user['clinica']['id'];
            $dados['lancado_por'] = $user['id'];
            $dados['status'] = $dados['status'] ?? 'pendente';

            // Calcular valor final
            $valor_nominal = $dados['valor_nominal'];
            $descontos = $dados['descontos'] ?? 0;
            $acrescimos = $dados['acrescimos'] ?? 0;
            $dados['valor_final'] = $valor_nominal - $descontos + $acrescimos;

            $conta = FinanceiroPagar::create($dados);

            return response()->json([
                'message' => 'Conta a pagar criada com sucesso',
                'data' => $conta->load(['clinica'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao criar conta a pagar: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $conta = FinanceiroPagar::with(['clinica'])->findOrFail($id);
        return response()->json($conta);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $conta = FinanceiroPagar::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'descricao' => 'sometimes|string|max:255',
            'valor_nominal' => 'sometimes|numeric|min:0.01',
            'data_vencimento' => 'sometimes|date',
            'fornecedor_id' => 'nullable|integer',
            'paciente_id' => 'nullable|integer',
            'referencia' => 'nullable|string|max:255',
            'notas' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Dados inválidos',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $dados = $request->all();
            
            // Recalcular valor final se necessário
            if (isset($dados['valor_nominal']) || isset($dados['descontos']) || isset($dados['acrescimos'])) {
                $valor_nominal = $dados['valor_nominal'] ?? $conta->valor_nominal;
                $descontos = $dados['descontos'] ?? $conta->descontos ?? 0;
                $acrescimos = $dados['acrescimos'] ?? $conta->acrescimos ?? 0;
                $dados['valor_final'] = $valor_nominal - $descontos + $acrescimos;
            }

            $conta->update($dados);
            
            return response()->json([
                'message' => 'Conta a pagar atualizada com sucesso',
                'data' => $conta->fresh(['clinica'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao atualizar conta a pagar: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $conta = FinanceiroPagar::findOrFail($id);

        try {
            $conta->delete();
            return response()->json(['message' => 'Conta a pagar removida com sucesso']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao remover conta a pagar: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Marcar conta como paga
     */
    public function marcarComoPago(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'data_pagamento' => 'nullable|date',
            'meio_pagamento' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Dados inválidos',
                'errors' => $validator->errors(),
            ], 422);
        }

        $conta = FinanceiroPagar::findOrFail($id);

        try {
            $conta->update([
                'status' => 'pago',
                'data_pagamento' => $request->get('data_pagamento', now()->toDateString()),
                'meio_pagamento' => $request->get('meio_pagamento'),
            ]);

            return response()->json([
                'message' => 'Conta marcada como paga',
                'data' => $conta->fresh(['clinica'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao marcar conta como paga: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Obter estatísticas de contas a pagar
     */
    public function estatisticas(Request $request)
    {
        try {
            $query = FinanceiroPagar::query();

            if ($request->has('data_inicio') && $request->has('data_fim')) {
                $query->whereBetween('data_vencimento', [
                    $request->data_inicio,
                    $request->data_fim
                ]);
            }

            $total_pendente = (clone $query)->where('status', 'pendente')->sum('valor_final');
            $total_pago = (clone $query)->where('status', 'pago')->sum('valor_final');
            $total_vencido = (clone $query)
                ->where('status', 'pendente')
                ->where('data_vencimento', '<', now()->toDateString())
                ->sum('valor_final');

            $count_pendente = (clone $query)->where('status', 'pendente')->count();
            $count_pago = (clone $query)->where('status', 'pago')->count();
            $count_vencido = (clone $query)
                ->where('status', 'pendente')
                ->where('data_vencimento', '<', now()->toDateString())
                ->count();

            return response()->json([
                'totais' => [
                    'pendente' => $total_pendente,
                    'pago' => $total_pago,
                    'vencido' => $total_vencido,
                ],
                'quantidades' => [
                    'pendente' => $count_pendente,
                    'pago' => $count_pago,
                    'vencido' => $count_vencido,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao obter estatísticas: ' . $e->getMessage(),
            ], 500);
        }
    }
}
