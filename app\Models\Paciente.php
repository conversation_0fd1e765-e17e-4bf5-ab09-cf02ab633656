<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
class Paciente extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'pacientes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id_ficha',
        'nome',
        'data_nascimento',
        'cpf',
        'etnia',
        'como_conheceu',
        'observacoes',
        'endereco_cep',
        'endereco_logradouro',
        'endereco_numero',
        'endereco_complemento',
        'endereco_cidade',
        'endereco_estado',
        'diagnostico',
        'prognostico',
        'tratamento',
        'data_inicio_tratamento',
        'data_final_prevista',
        'status_tratamento',
        'primeira_consulta',
        'ultima_consulta',
        'proxima_consulta',
        'rg',
        'nome_pai',
        'nome_mae',
        'responsavel_nome',
        'responsavel_cpf',
        'responsavel_rg',
        'consultas_realizadas',
        'formulario_respondido',
        'public_token',
        'clinica_id',
        'dentista_id',
        'profile_picture_url',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'nome'
        ];
    }

    protected static function booted() {
        static::addGlobalScope(new \App\Scopes\ClinicaScope);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    public function consultas(): HasMany
    {
        return $this->hasMany(Consulta::class);
    }

    public function imagens(): HasMany
    {
        return $this->hasMany(Imagem::class);
    }

    public function fases_tratamento(): HasMany
    {
        return $this->hasMany(FaseTratamento::class);
    }

    public function metas_terapeuticas(): HasMany
    {
        return $this->hasMany(MetaTerapeutica::class);
    }

    public function mentoria(): HasOne
    {
        return $this->hasOne(Mentoria::class);
    }

    public function clinica(): HasOne
    {
        return $this->hasOne(Clinica::class, 'id', 'clinica_id');
    }

    public function dentista(): HasOne
    {
        return $this->hasOne(Dentista::class, 'id', 'dentista_id');
    }

    public function formulario_boas_vindas(): HasOne
    {
        return $this->hasOne(FormularioBoasVindas::class, 'id_paciente', 'id');
    }

    public function detalhes_paciente(): HasMany
    {
        return $this->hasMany(DetalhePaciente::class);
    }

    public function contatos(): HasMany
    {
        return $this->hasMany(ContatoPaciente::class);
    }

    public function tratamentos_sugeridos(): BelongsToMany
    {
        return $this->belongsToMany(TratamentoSugerido::class);
    }

    public function fatores_clinicos(): BelongsToMany
    {
        return $this->belongsToMany(FatorClinico::class);
    }

    public function fatores_diagnostico(): BelongsToMany
    {
        return $this->belongsToMany(FatorDiagnostico::class);
    }

    public function necessidades_encaminhamentos(): HasMany
    {
        return $this->hasMany(NecessidadeEncaminhamento::class);
    }

    public function aparatologia(): HasOne
    {
        return $this->hasOne(Aparatologia::class);
    }

    public function historicos(): HasMany
    {
        return $this->hasMany(HistoricoPaciente::class);
    }

    public function modelos3d(): HasMany
    {
        return $this->hasMany(Modelo3D::class);
    }
}

