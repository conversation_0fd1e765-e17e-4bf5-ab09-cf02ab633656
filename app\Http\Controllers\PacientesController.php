<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Clinica;
use App\Models\Dentista;
use App\Models\Paciente;
use App\Models\FichaCounter;
use App\Models\ContatoPaciente;
use App\Models\DetalhePaciente;
use App\Models\FormularioBoasVindas;
use App\Models\QuestaoFormularioBoasVindas;
use App\Models\HistoricoPaciente;
use App\Traits\LogsActionHistory;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class PacientesController extends Controller
{
    use LogsActionHistory;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        \Log::info("👥 Pacientes::index() chamado");

        $user = auth()->payload();

        $body = $request->all();
        if (isset($body) && isset($body['search']))
            $search = $body['search'];
        else
            $search = '';

        if (trim($search) === '')
            $pacientes = Paciente::with('consultas')->get();
        else
            $pacientes = Paciente::with('consultas')
                ->where('nome', 'like', '%' . $search . '%')
                ->get();

        \Log::info("📊 Pacientes encontrados", [
            'count' => $pacientes->count(),
            'clinicas' => $pacientes->pluck('clinica_id')->unique()->toArray()
        ]);

        return response()->json($pacientes);
    }
    /**
     * Display a listing of the resource.
     */
    public function search(Request $request)
    {
        $user = auth()->payload();

        $body = $request->all();
        if (isset($body) && isset($body['search']))
            $search = $body['search'];
        else
            $search = '';

        $query = Paciente::with('dentista');

        if (trim($search) !== '') {
            $query->where('nome', 'like', '%' . $search . '%');
        }

        $pacientes = $query->orderBy('id', 'desc')->get();

        // Adiciona a URL da imagem de perfil e o nome do dentista a todos os resultados
        $pacientes = $pacientes->map(function ($paciente) {
            $pacienteArray = $paciente->toArray();
            $pacienteArray['profile_picture_url'] = imageUrl($pacienteArray['profile_picture_url']);
            $pacienteArray['dentista'] = isset($pacienteArray['dentista']['nome']) ? $pacienteArray['dentista']['nome'] : null;
            return $pacienteArray;
        });

        return response()->json($pacientes);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return 'create()';
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request) {
        $body = $request->all();
        $user = auth()->payload();

        $paciente = new Paciente();
        $token = createToken(50);
        $paciente->public_token = $token;
        $paciente->nome = $body['nome'];
        $paciente->observacoes = isset($body['observacoes']) ? $body['observacoes'] : '';


        if (!$user['system_admin']) {
            $paciente->clinica_id = $user['clinica']['id'];

            $dentista = Dentista::find($body['dentista_id']);
            if (!$dentista || $dentista->clinica_id !== $user['clinica']['id']) {
                return responseError(['message' => 'Dentista não encontrado ou não pertence à clínica atual']);
            }
        }
        else {
            // if ($body['clinica_id'] == 'add') {
            //     $clinica = new Clinica();
            //     $clinica->nome = $body['novaClinica'];
            //     $clinica->slug = Str::slug(mb_strtolower(preg_replace('/[\s]/', '-', $clinica->nome)), '-');
            //     $clinica->save();
            //     $paciente->clinica_id = $clinica->id;
            // } else {
            $paciente->clinica_id = $body['clinica_id'];
            // }
        }

        $paciente->dentista_id = $body['dentista_id'];

        $paciente->id_ficha = FichaCounter::getNextIdFicha($paciente->clinica_id);

        $paciente->save();

        // Log the patient creation action
        $this->logCreateAction($paciente, null, $request, "Created new patient: {$paciente->nome}");

        $celular = $body['celular'];
        $celular_whatsapp = $body['celular_whatsapp'];

        if ($celular) {
            $contatoPaciente = new ContatoPaciente();
            $contatoPaciente->tipo = $celular_whatsapp == 'true' ? 'whatsapp' : 'celular';
            $contatoPaciente->contato = $celular;
            $contatoPaciente->paciente_id = $paciente->id;
            $contatoPaciente->save();

            // Log the contact creation action
            $this->logCreateAction($contatoPaciente, null, $request, "Added contact for patient: {$paciente->nome}");
        }

        return responseSuccess(['data' => $paciente]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();

        // O $id recebido via parâmetro representa o ID da ficha - cada clínica tem sua id_ficha incrementada independentemente
        $id_ficha = $id;

        $paciente = Paciente::with([
            'fases_tratamento' => function ($query) {
                $query->orderBy('data_inicio', 'ASC');
            },
            'detalhes_paciente',
            'metas_terapeuticas',
            'contatos',
            'mentoria',
            'tratamentos_sugeridos',
            'necessidades_encaminhamentos',
            'fatores_clinicos',
            'fatores_diagnostico',
            'imagens',
            'clinica',
            'dentista',
            'aparatologia',
            'consultas'
        ])
            ->where('id_ficha', $id_ficha)
            ->firstOrFail();

        $paciente['fase_atual'] = $this->getFaseAtual($paciente['fases_tratamento']);

        $paciente['profile_picture_url'] = imageUrl($paciente['profile_picture_url']);

        foreach ($paciente['imagens'] as &$imagem)
            $imagem['url'] = imageUrl($imagem['url']);

        return response()->json($paciente);
    }

    /**
     * Display the specified resource with clinica_slug filtering.
     */
    public function showWithClinicaSlug(string $clinica_slug, string $id)
    {
        $user = auth()->payload();

        // O $id recebido via parâmetro representa o ID da ficha - cada clínica tem sua id_ficha incrementada independentemente
        $id_ficha = $id;

        $clinica = Clinica::where('slug', $clinica_slug)->first();

        if (!$clinica) {
            return response()->json(['error' => 'Clinica not found.'], 404);
        }

        // Verificar permissões:
        // - System admin pode acessar qualquer clínica
        // - Usuário normal só pode acessar sua própria clínica
        if (!$user['system_admin'] && $clinica->id !== $user['clinica']['id']) {
            return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
        }

        // Remove o ClinicaScope para admins poderem acessar pacientes de outras clínicas
        $query = Paciente::withoutGlobalScope(\App\Scopes\ClinicaScope::class)
            ->with([
                'fases_tratamento' => function ($query) {
                    $query->orderBy('data_inicio', 'ASC');
                },
                'detalhes_paciente',
                'metas_terapeuticas',
                'contatos',
                'mentoria',
                'tratamentos_sugeridos',
                'fatores_clinicos',
                'imagens',
                'clinica',
                'dentista',
            ])
            ->where('clinica_id', $clinica->id)
            ->where('id_ficha', $id_ficha);

        $paciente = $query->first();

        if (!$paciente) {
            return response()->json(['error' => 'Paciente not found for this clinica.'], 404);
        }

        $paciente['fase_atual'] = $this->getFaseAtual($paciente['fases_tratamento']);

        $paciente['profile_picture_url'] = imageUrl($paciente['profile_picture_url']);

        foreach ($paciente['imagens'] as &$imagem)
            $imagem['url'] = imageUrl($imagem['url']);

        return response()->json($paciente);
    }


    public function getClinicaPacientes(string $clinica_slug)
    {
        $user = auth()->payload();

        $clinica = Clinica::where('slug', $clinica_slug)->first();

        if (!$clinica) {
            return response()->json(['error' => 'Clinica not found.'], 404);
        }

        // Verificar permissões:
        // - System admin pode acessar qualquer clínica
        // - Usuário normal só pode acessar sua própria clínica
        if (!$user['system_admin'] && $clinica->id !== $user['clinica']['id']) {
            return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
        }

        // Remove o ClinicaScope para admins poderem acessar pacientes de outras clínicas
        $pacientes = Paciente::withoutGlobalScope(\App\Scopes\ClinicaScope::class)
            ->with(['contatos', 'dentista'])
            ->where('clinica_id', $clinica->id)
            ->orderBy('nome', 'asc')
            ->get();

        return response()->json($pacientes);
    }

    public function getByToken(string $token)
    {
        $paciente = Paciente::with([
            'contatos'
        ])->where('public_token', $token)->first();

        if (!$paciente) {
            return response()->json(['error' => 'Paciente not found'], 404);
        }

        $data = [
            'id' => $paciente->id,
            'nome' => $paciente->nome,
            'data_nascimento' => $paciente->data_nascimento,
        ];

        // Extract email and whatsapp from contatos
        $email = $paciente->contatos->firstWhere('tipo', 'email');
        $whatsapp = $paciente->contatos->firstWhere('tipo', 'whatsapp');

        if ($email) {
            $data['email'] = $email->contato; // assuming valor is the field with the email value
        }

        if ($whatsapp) {
            $data['whatsapp'] = $whatsapp->contato; // assuming valor is the field with the whatsapp value
        }

        return response()->json([
            'status' => 'success',
            'paciente' => $data
        ]);
    }

    public function getFichaInicialByToken(string $token)
    {
        try {
            $paciente = Paciente::where('public_token', $token)->first();

            if (!$paciente) {
                return response()->json(['error' => 'Paciente not found'], 404);
            }

            // Check if WelcomeForm object exists with the paciente ID
            $welcomeForm = $paciente->formulario_boas_vindas;

            if (!$welcomeForm) {
                return responseSuccess([
                    'ficha_respondida' => false,
                ]);
            }

            $questoes = $welcomeForm->questoes;

            foreach ($questoes as $questao) {
                if (isset($questao['alternativas']) && $questao['alternativas'] != '') {
                    $questao['alternativas'] = json_decode($questao['alternativas'], true);
                }

                $questao['textOptions'] = $questao['text_options'];
                unset($questao['text_options']);

                $questao['resposta'] = $questao['respostas'];
                unset($questao['respostas']);
            }

            $response = responseSuccess([
                'ficha_respondida' => true,
                'data_resposta' => $welcomeForm->created_at,
                'questoes' => $questoes->toArray(),
            ]);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    public function getFichaInicial($paciente_id)
    {
        try {
            if (!$paciente_id)
                throw new \Exception('O paciente não foi especificado');

            // Check if paciente exists with the given ID
            $paciente = Paciente::find($paciente_id);
            if (!$paciente)
                throw new \Exception('Paciente não encontrado');

            // Check if WelcomeForm object exists with the paciente ID
            $welcomeForm = $paciente->formulario_boas_vindas;

            if (!$welcomeForm) {
                return responseSuccess([
                    'ficha_respondida' => false,
                ]);
            }

            $questoes = $welcomeForm->questoes;

            foreach ($questoes as $questao) {
                if (isset($questao['alternativas']) && $questao['alternativas'] != '') {
                    $questao['alternativas'] = json_decode($questao['alternativas'], true);
                }

                $questao['textOptions'] = $questao['text_options'];
                unset($questao['text_options']);

                $questao['resposta'] = $questao['respostas'];
                unset($questao['respostas']);
            }

            $response = responseSuccess([
                'ficha_respondida' => true,
                'data_resposta' => $welcomeForm->created_at,
                'questoes' => $questoes->toArray(),
            ]);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    public function salvarDiagnostico(Request $request)
    {
        $paciente_id = $request->input('paciente_id');
        $diagnostico = $request->input('diagnostico');

        $paciente = Paciente::find($paciente_id);
        if (!$paciente) {
            return responseError(['message' => 'Paciente não encontrado']);;
        }

        $paciente->diagnostico = $diagnostico;
        $paciente->save();

        return responseSuccess();
    }

    public function salvarPrognostico(Request $request)
    {
        $paciente_id = $request->input('paciente_id');
        $prognostico = $request->input('prognostico');

        $paciente = Paciente::find($paciente_id);
        if (!$paciente) {
            return responseError(['message' => 'Paciente não encontrado']);;
        }

        $paciente->prognostico = $prognostico;
        $paciente->save();

        return responseSuccess();
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {

            $paciente_id = $id;
            $existingPaciente = Paciente::find($paciente_id);
            if (!$existingPaciente)
                throw new Exception('Paciente não encontrado');

            // Capture original data before update
            $originalData = $this->captureOriginalData($existingPaciente);

            $pacienteData = $request->all();

            // Edit the profile_picture_url before filling the model, if it exists
            if (isset($pacienteData['profile_picture_url'])) {
                $pacienteData['profile_picture_url'] = relativeUrl($pacienteData['profile_picture_url']);
            }

            // Update the paciente model with the new data from the request
            $existingPaciente->fill($pacienteData);

            // Update the related MeioDeContato models
            // $meiosDeContato = $request->input('paciente.meios_de_contato');
            // $paciente->meiosDeContato()->sync($meiosDeContato);

            $existingPaciente->save();

            // Log the patient update action
            $this->logUpdateAction($existingPaciente, $originalData, null, $request, "Updated patient: {$existingPaciente->nome}");

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log detalhado do erro
            \Log::error('Erro ao atualizar paciente:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $user = auth()->payload();

            // Find the patient
            $paciente = Paciente::find($id);

            // Check if patient exists
            if (!$paciente) {
                return responseError(['message' => 'Paciente não encontrado']);
            }

            // Check permissions: only allow deletion if user is from the same clinic or is a system admin
            if (!$user['system_admin'] && $paciente->clinica_id != $user['clinica']['id']) {
                return responseError(['message' => 'Não autorizado. Você só pode excluir pacientes da sua própria clínica'], 403);
            }

            // Capture patient data before deletion for logging
            $pacienteData = $paciente->toArray();

            // Começar uma transação para garantir que todas as operações sejam concluídas ou nenhuma
            DB::beginTransaction();

            try {
                // Excluir registros relacionados que não têm onDelete('cascade')
                // Analises
                DB::table('analises')->where('paciente_id', $paciente->id)->delete();

                // Excluir outros registros relacionados que podem não ter onDelete('cascade')
                DB::table('consultas')->where('paciente_id', $paciente->id)->delete();
                DB::table('imagens')->where('paciente_id', $paciente->id)->delete();
                DB::table('modelos3d')->where('paciente_id', $paciente->id)->delete();
                DB::table('fases_tratamento')->where('paciente_id', $paciente->id)->delete();
                DB::table('metas_terapeuticas')->where('paciente_id', $paciente->id)->delete();
                DB::table('mentorias')->where('paciente_id', $paciente->id)->delete();
                DB::table('detalhes_pacientes')->where('paciente_id', $paciente->id)->delete();
                DB::table('contatos_pacientes')->where('paciente_id', $paciente->id)->delete();
                DB::table('necessidades_encaminhamentos')->where('paciente_id', $paciente->id)->delete();
                DB::table('aparatologia')->where('paciente_id', $paciente->id)->delete();

                // Excluir registros de tabelas pivot
                DB::table('fator_clinico_paciente')->where('paciente_id', $paciente->id)->delete();
                DB::table('fator_diagnostico_paciente')->where('paciente_id', $paciente->id)->delete();
                DB::table('paciente_tratamento_sugerido')->where('paciente_id', $paciente->id)->delete();

                // Excluir formulário de boas-vindas
                $formulario = DB::table('formularios_boas_vindas')->where('id_paciente', $paciente->id)->first();
                if ($formulario) {
                    DB::table('questoes_formulario_boas_vindas')->where('id_formulario', $formulario->id)->delete();
                    DB::table('formularios_boas_vindas')->where('id_paciente', $paciente->id)->delete();
                }

                // Finalmente, excluir o paciente
                $paciente->delete();

                // Log the patient deletion action
                $this->logDeleteAction($paciente, $pacienteData, request(), "Deleted patient: {$pacienteData['nome']}");

                // Confirmar a transação
                DB::commit();

                return responseSuccess(['message' => 'Paciente excluído com sucesso']);
            } catch (\Exception $innerException) {
                // Se ocorrer algum erro, reverter todas as alterações
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $e) {
            // Log detailed error
            Log::error('Erro ao excluir paciente:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'paciente_id' => $id
            ]);

            return responseError([
                'message' => 'Erro ao excluir paciente: ' . $e->getMessage()
            ]);
        }
    }

    public function uploadImage(Request $request)
    {
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            $type = $request->input('type');
            $date = $request->input('date');
            $description = $request->input('description');

            $fileName = uniqid($type . '_' . rand(), true) . '.' . $image->getClientOriginalExtension();

            $img = Image::make($image->getRealPath());

            // Aplicar orientação automática baseada nos dados EXIF
            $img->orientate();

            // $img->resize(120, 120, function ($constraint) {
            //     $constraint->aspectRatio();
            // });

            $img->stream(); // <-- Key point

            //dd();
            Storage::disk('local')->put('images/' . $fileName, $img, 'public');

            return response()->json(['type' => $type, 'description' => $description, 'date' => $date]);
        }
    }

    public function submitWelcomeForm(Request $request)
    {
        try {
            // Delete existing FormularioBoasVindas and QuestaoFormularioBoasVindas records
            $existingFormulario = FormularioBoasVindas::where(
                'id_paciente',
                $request->input('id_paciente')
            )->first();
            if ($existingFormulario) {
                DetalhePaciente::where('paciente_id', $request->input('id_paciente'))->delete();

                QuestaoFormularioBoasVindas::where('id_formulario', $existingFormulario->id)->delete();
                $existingFormulario->delete();
            }

            Paciente::where('id', $request->input('id_paciente'))->update(['formulario_respondido' => true]);

            $formularioBoasVindas = new FormularioBoasVindas();
            $formularioBoasVindas->horario = date('Y-m-d H:i:s');
            $formularioBoasVindas->id_paciente = $request->input('id_paciente');

            $formularioBoasVindas->save();

            $idFormulario = $formularioBoasVindas->id;

            DB::transaction(function () use ($idFormulario, $request, &$formularioBoasVindas) {

                $questions = $request->input('questions') ? json_decode($request->input('questions'), true) : [];

                foreach ($questions as $question) {
                    switch ($question['id']) {
                        case 'nome_completo':
                            $formularioBoasVindas->nome = isset($question['resposta']) ? $question['resposta'] : null;
                            break;
                        case 'email':
                            $formularioBoasVindas->email = isset($question['resposta']) ? $question['resposta'] : null;
                            break;
                        case 'whatsapp':
                            $formularioBoasVindas->whatsapp = isset($question['resposta']) ? $question['resposta'] : null;
                            break;
                    }

                    $this->createDetalhePacienteByQuestao($request->input('id_paciente'), $question);

                    $questao = new QuestaoFormularioBoasVindas();
                    $questao->id_formulario = $idFormulario;
                    $questao->questao = $question['questao'];

                    $questao->input_id = $question['id'];
                    $questao->ordem = $question['ordem'];
                    $questao->obrigatoria = $question['obrigatoria'];

                    if (!isset($question['alternativas']) || $question['alternativas'] == '')
                        $question['alternativas'] = [];
                    $questao->alternativas = json_encode($question['alternativas']);

                    if (!isset($question['textOptions']) || $question['textOptions'] == '')
                        $question['textOptions'] = [];
                    $questao->text_options = json_encode($question['textOptions']);

                    $questao->tipo = $question['tipo'];
                    $questao->detalhes = isset($question['detalhe']) ? $question['detalhe'] : '';

                    if ($question['tipo'] == 'text' || $question['tipo'] == 'phone' || $question['tipo'] == 'date') {
                        $questao->respostas = isset($question['resposta']) ? $question['resposta'] : '';
                    } else if ($question['tipo'] == 'checkbox' || $question['tipo'] == 'radio') {
                        $respostas = [];
                        if (isset($question['alternativas']) && is_array($question['alternativas'])) {
                            foreach ($question['alternativas'] as $alternativa) {
                                if (!isset($alternativa['selecionada']) || !$alternativa['selecionada'])
                                    continue;

                                if ($question['tipo'] == 'checkbox') {
                                    $respostas[] = isset($alternativa['resposta']) ? $alternativa['resposta'] : '';
                                } else if ($question['tipo'] == 'radio') {
                                    $questao->respostas = isset($alternativa['resposta']) ? $alternativa['resposta'] : '';
                                }
                            }
                        }

                        if (sizeof($respostas) > 0)
                            $questao->respostas = json_encode($respostas);
                    }

                    $questao->save();

                    // Agora que você tem o ID da questão, você pode criar os detalhes do paciente
                    if (isset($question['alternativas']) && is_array($question['alternativas'])) {
                        foreach ($question['alternativas'] as $alternativa) {
                            if (!isset($alternativa['selecionada']) || !$alternativa['selecionada'])
                                continue;

                            $alternativa['questao_id'] = $questao->id;
                            $this->createDetalhePacienteByAlternativa($request->input('id_paciente'), $alternativa);
                        }
                    }
                }

                $formularioBoasVindas->save();
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    private function createDetalhePacienteByAlternativa($idPaciente, $alternativa)
    {
        $tipos = ['positivo', 'neutro', 'atencao', 'negativo'];

        foreach ($tipos as $tipo) {
            if (isset($alternativa["ponto_$tipo"]) && $alternativa["ponto_$tipo"] && $alternativa['selecionada'] == 'true') {
                $detalhePaciente = new DetalhePaciente();
                $detalhePaciente->paciente_id = $idPaciente;
                $detalhePaciente->tipo = isset($alternativa['tipo_detalhe']) ? $alternativa['tipo_detalhe'] : NULL;
                $resposta = isset($alternativa['resposta']) ? $alternativa['resposta'] : '';
                $detalhePaciente->detalhe = str_replace('$resposta', $resposta, $alternativa["ponto_$tipo"]);
                $detalhePaciente->nivel = $tipo;

                $detalhePaciente->save();
            }
        }
    }

    private function createDetalhePacienteByQuestao($idPaciente, $questao)
    {
        $tipos = ['positivo', 'neutro', 'atencao', 'negativo'];

        foreach ($tipos as $tipo) {
            if (isset($questao["ponto_$tipo"]) && $questao["ponto_$tipo"]) {
                $detalhePaciente = new DetalhePaciente();
                $detalhePaciente->paciente_id = $idPaciente;
                $detalhePaciente->tipo = isset($questao['tipo_detalhe']) ? $questao['tipo_detalhe'] : NULL;
                $resposta = isset($questao['resposta']) ? $questao['resposta'] : '';
                $detalhePaciente->detalhe = str_replace('$resposta', $resposta, $questao["ponto_$tipo"]);
                $detalhePaciente->nivel = $tipo;

                $detalhePaciente->save();
            }
        }
    }

    public function teste(Request $request)
    {
        $payload = auth()->payload();
        return $payload->get('customPayloadItem1');
    }

    private function getFaseAtual($fases_tratamento)
    {
        $fase_atual = null;

        $now = new \DateTime();
        foreach ($fases_tratamento as $fase) {
            $inicio = new \DateTime($fase['data_inicio']);
            $fim = new \DateTime($fase['data_fim']);
            if ($now >= $inicio && $now < $fim) {
                $fase_atual = $fase;
                break;
            }
        }

        return $fase_atual;
    }

    /**
     * Retorna uma lista simplificada de pacientes da clínica atual
     * contendo apenas os campos id, nome e id_ficha
     */
    public function listaSimples()
    {
        $user = auth()->payload();

        $pacientes = Paciente::select('id', 'nome', 'id_ficha')
            ->orderBy('nome')
            ->get()
            ->toArray();

        return responseSuccess($pacientes);
    }

    /**
     * Gera um slug único adicionando números se necessário
     */
    private function generateUniqueSlug($baseSlug)
    {
        $slug = $baseSlug;
        $counter = 2;

        // Verifica se o slug já existe
        while (Clinica::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
