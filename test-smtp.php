<?php

// Script de teste de SMTP
// Execute: php test-smtp.php

require __DIR__.'/vendor/autoload.php';

use Illuminate\Support\Facades\Mail;

// Carregar o Laravel
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Teste de Configuração SMTP ===\n\n";

echo "MAIL_MAILER: " . env('MAIL_MAILER') . "\n";
echo "MAIL_HOST: " . env('MAIL_HOST') . "\n";
echo "MAIL_PORT: " . env('MAIL_PORT') . "\n";
echo "MAIL_USERNAME: " . env('MAIL_USERNAME') . "\n";
echo "MAIL_ENCRYPTION: " . env('MAIL_ENCRYPTION') . "\n";
echo "MAIL_FROM_ADDRESS: " . env('MAIL_FROM_ADDRESS') . "\n";
echo "MAIL_VERIFY_PEER: " . env('MAIL_VERIFY_PEER', 'true') . "\n\n";

echo "Tentando enviar e-mail de teste...\n\n";

try {
    Mail::raw('Este é um e-mail de teste do sistema Lumi.', function($message) {
        $message->to(env('MAIL_USERNAME'))
                ->subject('Teste SMTP - Lumi System');
    });
    
    echo "✅ E-mail enviado com sucesso!\n";
    echo "Verifique a caixa de entrada de: " . env('MAIL_USERNAME') . "\n";
    
} catch (\Exception $e) {
    echo "❌ Erro ao enviar e-mail:\n";
    echo $e->getMessage() . "\n\n";
    
    // Sugestões baseadas no erro
    $errorMsg = $e->getMessage();
    
    if (strpos($errorMsg, 'certificate') !== false || strpos($errorMsg, 'SSL') !== false) {
        echo "💡 Sugestões:\n";
        echo "1. Adicione MAIL_VERIFY_PEER=false no .env\n";
        echo "2. Tente usar porta 465 com MAIL_ENCRYPTION=ssl\n";
        echo "3. Tente usar porta 25 sem criptografia\n";
    }
    
    if (strpos($errorMsg, 'authenticate') !== false || strpos($errorMsg, '535') !== false) {
        echo "💡 Sugestões:\n";
        echo "1. Verifique se o usuário e senha estão corretos\n";
        echo "2. Verifique se o servidor SMTP permite autenticação\n";
        echo "3. Tente usar o e-mail completo como username\n";
    }
    
    if (strpos($errorMsg, 'connect') !== false || strpos($errorMsg, 'Connection') !== false) {
        echo "💡 Sugestões:\n";
        echo "1. Verifique se o host está correto\n";
        echo "2. Verifique se a porta está aberta no firewall\n";
        echo "3. Tente pingar o servidor: ping smtp.lumivision.app\n";
    }
}

echo "\n=== Fim do Teste ===\n";

