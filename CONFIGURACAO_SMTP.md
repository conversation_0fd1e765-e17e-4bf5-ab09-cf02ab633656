# 📧 Configuração de SMTP para Reset de Senha

## 🎯 Onde Configurar o SMTP

As configurações de SMTP ficam no arquivo `.env` do backend Laravel:

**Localização:** `d:/Projetos/lumi-api4/.env`

## ⚙️ Configurações Necessárias

### 1. Gmail (Recomendado para testes)

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=sua-senha-de-app-do-gmail
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Ortho System"
```

**⚠️ IMPORTANTE:** Para Gmail, você precisa gerar uma "Senha de App":
1. Acesse: https://myaccount.google.com/security
2. Ative a verificação em duas etapas
3. Vá em "Senhas de app"
4. Gere uma senha para "E-mail"
5. Use essa senha no `MAIL_PASSWORD`

### 2. Outlook/Hotmail

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp-mail.outlook.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=sua-senha
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Ortho System"
```

### 3. SendGrid (Recomendado para produção)

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=sua-api-key-do-sendgrid
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Ortho System"
```

### 4. Mailgun

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=seu-usuario-mailgun
MAIL_PASSWORD=sua-senha-mailgun
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Ortho System"
```

### 5. SMTP Customizado (Seu próprio servidor)

```env
MAIL_MAILER=smtp
MAIL_HOST=mail.seudominio.com.br
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=sua-senha
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Ortho System"
```

## 🔧 Após Configurar

1. **Limpar cache do Laravel:**
   ```bash
   cd d:/Projetos/lumi-api4
   php artisan config:clear
   php artisan cache:clear
   ```

2. **Testar o envio de e-mail:**
   ```bash
   php artisan tinker
   ```
   
   Depois execute:
   ```php
   Mail::raw('Teste de e-mail', function($message) {
       $message->to('<EMAIL>')
               ->subject('Teste SMTP Lumi');
   });
   ```

## 🐛 Solução de Problemas

### Erro: "Connection refused"
- Verifique se a porta está correta (587 para TLS, 465 para SSL)
- Verifique se seu firewall não está bloqueando

### Erro: "Authentication failed"
- Verifique usuário e senha
- Para Gmail, use senha de app, não a senha normal
- Verifique se a conta permite acesso de apps menos seguros

### Erro: "SSL certificate problem"
- Adicione no `.env`:
  ```env
  MAIL_VERIFY_PEER=false
  ```

### E-mails não chegam
- Verifique a pasta de SPAM
- Verifique se o `MAIL_FROM_ADDRESS` é válido
- Teste com outro provedor de e-mail

## 📝 Logs de E-mail

Para ver os logs de e-mail durante desenvolvimento, você pode usar:

```env
MAIL_MAILER=log
```

Os e-mails serão salvos em: `storage/logs/laravel.log`

## 🔒 Segurança

**NUNCA** commite o arquivo `.env` com senhas reais!

Adicione no `.gitignore`:
```
.env
.env.backup
.env.production
```

## ✅ Checklist Final

- [ ] Configurações de SMTP no `.env`
- [ ] `FRONTEND_URL` configurado corretamente
- [ ] Cache limpo (`php artisan config:clear`)
- [ ] Teste de envio realizado
- [ ] E-mail de teste recebido
- [ ] Verificar pasta de SPAM
- [ ] Configurações não commitadas no Git

## 🎨 Personalização do E-mail

Os templates de e-mail estão em:
- **Layout:** `resources/views/emails/layout.blade.php`
- **Reset de senha:** `resources/views/emails/password-reset.blade.php`

Você pode personalizar:
- Cores
- Logo
- Textos
- Footer
- Links

## 📞 Suporte

Se precisar de ajuda, verifique:
1. Logs do Laravel: `storage/logs/laravel.log`
2. Logs do servidor web
3. Documentação do provedor de SMTP escolhido

