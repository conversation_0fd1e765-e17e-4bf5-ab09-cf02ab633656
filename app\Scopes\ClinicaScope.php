<?php
// App/Scopes/ClinicaScope.php
namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ClinicaScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        // Não aplicar o scope em rotas de autenticação para evitar problemas com token blacklisted
        $request = request();
        if ($request && in_array($request->path(), ['auth/refresh', 'auth/login', 'auth/logout'])) {
            return;
        }

        if (auth()->check()) {
            try {
                $user = auth()->payload();

                // Aplica o filtro para TODOS os usuários (incluindo admins)
                // Para admins que precisam acessar outras clínicas, o scope deve ser removido manualmente na rota
                if ($user && isset($user['clinica'])) {
                    $builder->where($model->getTable() . '.clinica_id', $user['clinica']['id']);
                }
            } catch (\Tymon\JWTAuth\Exceptions\TokenBlacklistedException $e) {
                // Token blacklisted - não aplicar o scope
                // Isso pode acontecer durante o refresh do token
                return;
            } catch (\Exception $e) {
                // Qualquer outro erro - não aplicar o scope
                return;
            }
        }
    }
}
