<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinanceiroPagar extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'financeiro_pagar';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'caixa_id',
        'conta_id',
        'clinica_id',
        'pagador_nome',
        'pagador_tipo',
        'fornecedor_id',
        'paciente_id',
        'contrato_codigo',
        'referencia',
        'descricao',
        'notas',
        'valor_nominal',
        'descontos',
        'acrescimos',
        'valor_final',
        'data_emissao',
        'data_vencimento',
        'data_pagamento',
        'meio_pagamento',
        'parcela',
        'parcelas_total',
        'status',
        'lancado_por',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'nome'
        ];
    }

    protected static function booted() {
        static::addGlobalScope(new \App\Scopes\ClinicaScope);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class, 'clinica_id');
    }

    public function paciente(): BelongsTo
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }

    public function lancadoPor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'lancado_por');
    }

    // ==================== SCOPES ====================

    public function scopeDaClinica($query, $clinicaId)
    {
        return $query->where('clinica_id', $clinicaId);
    }

    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopePagas($query)
    {
        return $query->where('status', 'pago');
    }

    public function scopeVencidas($query)
    {
        return $query->where('status', 'pendente')
                    ->where('data_vencimento', '<', now()->toDateString());
    }
}
